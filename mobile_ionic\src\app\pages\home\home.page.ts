import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule, ToastController, ModalController } from '@ionic/angular';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { Subscription, interval } from 'rxjs';
import { NotificationService } from '../../services/notification.service';
import { EmergencyOverlayService } from '../../services/emergency-overlay.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.page.html',
  styleUrls: ['./home.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule]
})
export class HomePage implements OnInit, OnDestroy {
  unreadNotificationCount = 0;
  private notificationSubscription: Subscription | null = null;
  private pollSubscription: Subscription | null = null;

  constructor(
    private router: Router,
    private toastCtrl: ToastController,
    private http: HttpClient,
    private notificationService: NotificationService,
    private emergencyOverlay: EmergencyOverlayService,
    private modalCtrl: ModalController
  ) {
    // Make emergency overlay service available for testing in browser console
    (window as any).testEmergency = this.emergencyOverlay;
  }

  ngOnInit() {
    // Subscribe to notification count updates
    this.notificationSubscription = this.notificationService.unreadCount$.subscribe(count => {
      this.unreadNotificationCount = count;
    });

    // Load initial unread count
    this.loadUnreadCount();

    // Poll for unread count every 30 seconds
    this.pollSubscription = interval(30000).subscribe(() => {
      this.notificationService.refreshUnreadCount();
    });
  }

  ngOnDestroy() {
    if (this.notificationSubscription) {
      this.notificationSubscription.unsubscribe();
    }
    if (this.pollSubscription) {
      this.pollSubscription.unsubscribe();
    }
  }



  openDisasterMap(disasterType: string) {
    console.log(`🏠 HOME: Opening disaster-specific map for: ${disasterType}`);

    // Map the disaster type to display names and routes
    let displayName = disasterType;
    let route = '';

    if (disasterType === 'earthquake') {
      displayName = 'Earthquake';
      route = '/earthquake-map';
    } else if (disasterType === 'typhoon') {
      displayName = 'Typhoon';
      route = '/typhoon-map';
    } else if (disasterType === 'flashflood') {
      displayName = 'Flash Flood';
      route = '/flood-map';
    } else if (disasterType === 'fire') {
      displayName = 'Fire';
      route = '/fire-map';
    } else if (disasterType === 'landslide') {
      displayName = 'Landslide';
      route = '/landslide-map';
    }

    console.log(`🏠 HOME: Navigating to ${route} for ${displayName}`);

    // Show loading toast
    this.toastCtrl.create({
      message: `🗺️ Opening ${displayName} evacuation centers...`,
      duration: 2000,
      color: 'primary'
    }).then(toast => toast.present());

    // Navigate to the disaster-specific map
    this.router.navigate([route]);
  }

  viewMap() {
    console.log(`🏠 HOME: Opening complete evacuation centers map`);

    // Show loading toast
    this.toastCtrl.create({
      message: '🗺️ Opening complete evacuation centers map...',
      duration: 2000,
      color: 'secondary'
    }).then(toast => toast.present());

    // Navigate to the all-maps page
    this.router.navigate(['/all-maps']);
  }

  async loadUnreadCount() {
    try {
      // Use the notification service instead of direct HTTP call
      await this.notificationService.refreshUnreadCount();

      // Subscribe to unread count updates
      this.notificationService.unreadCount$.subscribe(count => {
        this.unreadNotificationCount = count;
      });
    } catch (error) {
      console.error('Error loading unread notification count:', error);
    }
  }

  openNotifications() {
    this.router.navigate(['/notifications']);
  }

  async openUserGuide() {
    const modal = await this.modalCtrl.create({
      component: UserGuideModalComponent,
      cssClass: 'user-guide-modal'
    });
    await modal.present();
  }
}

// User Guide Modal Component
@Component({
  template: `
    <ion-header>
      <ion-toolbar>
        <ion-title class="modal-title">ALERTO User Guide</ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="dismiss()">Close</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content class="ion-padding">
      <!-- App Purpose Section -->
      <div class="guide-section">
        <h3 class="section-title">🛡️ Welcome to ALERTO - The Safe Zone</h3>
        <p class="section-content">
          ALERTO is your comprehensive disaster preparedness and evacuation assistance app.
          Our mission is to keep you safe by providing real-time access to evacuation centers,
          emergency contacts, and safety information during natural disasters.
        </p>
      </div>

      <!-- Main Features Section -->
      <div class="guide-section">
        <h3 class="section-title">✨ Main Features</h3>
        <div class="feature-list">
          <div class="feature-item">
            <img src="assets/home1.png" class="feature-icon" />
            <div class="feature-text">
              <h4>Disaster Selection</h4>
              <p>Choose from 6 disaster types to find specific evacuation centers</p>
            </div>
          </div>
          <div class="feature-item">
            <img src="assets/map1.png" class="feature-icon" />
            <div class="feature-text">
              <h4>Interactive Maps</h4>
              <p>View evacuation centers with real-time navigation and routing</p>
            </div>
          </div>
          <div class="feature-item">
            <img src="assets/search1.png" class="feature-icon" />
            <div class="feature-text">
              <h4>Location Search</h4>
              <p>Find specific locations and nearby evacuation centers</p>
            </div>
          </div>
          <div class="feature-item">
            <img src="assets/lamp1.png" class="feature-icon" />
            <div class="feature-text">
              <h4>Safety Tips & Contacts</h4>
              <p>Access emergency contacts and disaster-specific safety information</p>
            </div>
          </div>
        </div>
      </div>

      <!-- How to Use Section -->
      <div class="guide-section">
        <h3 class="section-title">📱 How to Use ALERTO</h3>
        <div class="steps-list">
          <div class="step-item">
            <div class="step-number">1</div>
            <div class="step-content">
              <h4>Select Disaster Type</h4>
              <p>From the home screen, tap on the disaster type you need help with (Earthquake, Typhoon, Flood, Fire, Landslide, or General)</p>
            </div>
          </div>
          <div class="step-item">
            <div class="step-number">2</div>
            <div class="step-content">
              <h4>View Evacuation Centers</h4>
              <p>The map will show evacuation centers specific to your selected disaster type with your current location</p>
            </div>
          </div>
          <div class="step-item">
            <div class="step-number">3</div>
            <div class="step-content">
              <h4>Use Map Controls</h4>
              <p>• Tap the house icon to see all centers list<br>• Tap download to save map offline<br>• Tap compass to route to 2 nearest centers</p>
            </div>
          </div>
          <div class="step-item">
            <div class="step-number">4</div>
            <div class="step-content">
              <h4>Navigate to Safety</h4>
              <p>Choose walking, cycling, or driving routes to reach your selected evacuation center safely</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Disaster Types Section -->
      <div class="guide-section">
        <h3 class="section-title">🌪️ Supported Disaster Types</h3>
        <div class="disaster-grid">
          <div class="disaster-type">
            <img src="assets/earthquake-icon.svg" class="disaster-icon" />
            <span>Earthquake</span>
          </div>
          <div class="disaster-type">
            <img src="assets/icon/bagyo.png" class="disaster-icon" />
            <span>Typhoon</span>
          </div>
          <div class="disaster-type">
            <img src="assets/flood.png" class="disaster-icon" />
            <span>Flood</span>
          </div>
          <div class="disaster-type">
            <img src="assets/fireIcon.png" class="disaster-icon" />
            <span>Fire</span>
          </div>
          <div class="disaster-type">
            <img src="assets/landslideIcon.png" class="disaster-icon" />
            <span>Landslide</span>
          </div>
          <div class="disaster-type">
            <img src="assets/otherdisasterIcon.png" class="disaster-icon" />
            <span>General</span>
          </div>
        </div>
      </div>

      <!-- Emergency Tips Section -->
      <div class="guide-section">
        <h3 class="section-title">🚨 Emergency Tips</h3>
        <div class="tips-list">
          <div class="tip-item">
            <ion-icon name="call-outline" class="tip-icon"></ion-icon>
            <p>Always call 911 for immediate emergency assistance</p>
          </div>
          <div class="tip-item">
            <ion-icon name="location-outline" class="tip-icon"></ion-icon>
            <p>Enable location services for accurate evacuation center directions</p>
          </div>
          <div class="tip-item">
            <ion-icon name="download-outline" class="tip-icon"></ion-icon>
            <p>Download maps when you have internet for offline access during emergencies</p>
          </div>
          <div class="tip-item">
            <ion-icon name="notifications-outline" class="tip-icon"></ion-icon>
            <p>Keep notifications enabled to receive emergency alerts</p>
          </div>
        </div>
      </div>

      <!-- Contact Section -->
      <div class="guide-section">
        <h3 class="section-title">📞 Need Help?</h3>
        <p class="section-content">
          For technical support or questions about ALERTO, visit the Tips tab for emergency contacts
          and safety information. Stay safe and prepared!
        </p>
      </div>
    </ion-content>
  `,
  styles: [`
    .modal-title {
      font-size: 1.3rem;
      font-weight: bold;
      color: var(--ion-color-primary);
    }
    .guide-section {
      margin-bottom: 25px;
      padding-bottom: 20px;
      border-bottom: 1px solid var(--ion-color-light);
    }
    .guide-section:last-child {
      border-bottom: none;
    }
    .section-title {
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 15px;
      color: var(--ion-color-primary);
    }
    .section-content {
      font-size: 0.95rem;
      line-height: 1.5;
      color: var(--ion-color-medium-shade);
    }
    .feature-list, .steps-list {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }
    .feature-item, .step-item {
      display: flex;
      align-items: flex-start;
      gap: 15px;
      padding: 12px;
      background: var(--ion-color-light);
      border-radius: 10px;
    }
    .feature-icon {
      width: 32px;
      height: 32px;
      object-fit: contain;
      flex-shrink: 0;
    }
    .feature-text h4, .step-content h4 {
      font-size: 1rem;
      font-weight: 600;
      margin-bottom: 5px;
      color: var(--ion-color-dark);
    }
    .feature-text p, .step-content p {
      font-size: 0.9rem;
      color: var(--ion-color-medium-shade);
      margin: 0;
      line-height: 1.4;
    }
    .step-number {
      width: 30px;
      height: 30px;
      background: var(--ion-color-primary);
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 0.9rem;
      flex-shrink: 0;
    }
    .disaster-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 15px;
    }
    .disaster-type {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      padding: 15px;
      background: var(--ion-color-light);
      border-radius: 10px;
      text-align: center;
    }
    .disaster-icon {
      width: 40px;
      height: 40px;
      object-fit: contain;
    }
    .disaster-type span {
      font-size: 0.85rem;
      font-weight: 500;
      color: var(--ion-color-dark);
    }
    .tips-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }
    .tip-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 10px;
      background: var(--ion-color-light);
      border-radius: 8px;
    }
    .tip-icon {
      color: var(--ion-color-primary);
      font-size: 1.2rem;
      flex-shrink: 0;
    }
    .tip-item p {
      margin: 0;
      font-size: 0.9rem;
      color: var(--ion-color-medium-shade);
    }
  `],
  standalone: true,
  imports: [IonicModule, CommonModule]
})
export class UserGuideModalComponent {
  constructor(private modalCtrl: ModalController) {}

  dismiss() {
    this.modalCtrl.dismiss();
  }
}