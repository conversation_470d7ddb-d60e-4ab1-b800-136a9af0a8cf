import{a as R}from"./chunk-OZ3L2UU6.js";import{a as p}from"./chunk-WPPT3EJF.js";import"./chunk-2GT6F2KJ.js";import"./chunk-2LL5MXLB.js";import{B as u,C as d,Eb as C,F as z,G as h,Ib as M,J as e,Jb as O,K as t,L as o,P as l,Q as D,X as n,Xb as q,Yb as P,Z as H,Zb as w,a as k,ca as x,ec as y,f as E,fc as L,gc as S,i as I,k as T,m as f,na as F,pa as b,qa as j,ra as g,vb as $,wb as _,xb as v,ya as U,yb as N,zb as A}from"./chunk-SFXIJNIZ.js";import"./chunk-LR6AIEJQ.js";import"./chunk-6NVMNNPA.js";import"./chunk-KW2BML7M.js";import"./chunk-SV2ZKNWA.js";import"./chunk-YJDO75HI.js";import"./chunk-HC6MZPB3.js";import"./chunk-Q5Q6EGIP.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-ZJ5IMUT4.js";import"./chunk-SGSBBWFA.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-EI2QJP5N.js";import"./chunk-W6U2AR23.js";import"./chunk-APL3YEA6.js";import"./chunk-HSXX7Y3C.js";import"./chunk-FUGLTCJS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import{g as m}from"./chunk-2R6CW7ES.js";var G=(()=>{class c{constructor(i){this.http=i,this.unreadCountSubject=new k(0),this.unreadCount$=this.unreadCountSubject.asObservable()}getHeaders(){let i=localStorage.getItem("auth_token");return new j({Authorization:`Bearer ${i}`,"Content-Type":"application/json"})}getNotifications(i=1,s=20,r){let a={page:i,per_page:s};return r&&(a.type=r),this.http.get(`${p.apiUrl}/notifications`,{headers:this.getHeaders(),params:a})}getUnreadCount(){return this.http.get(`${p.apiUrl}/notifications/unread-count`,{headers:this.getHeaders()})}markAsRead(i){return this.http.put(`${p.apiUrl}/notifications/${i}/read`,{},{headers:this.getHeaders()})}markAllAsRead(){return this.http.put(`${p.apiUrl}/notifications/mark-all-read`,{},{headers:this.getHeaders()})}deleteNotification(i){return this.http.delete(`${p.apiUrl}/notifications/${i}`,{headers:this.getHeaders()})}addReaction(i){return this.http.post(`${p.apiUrl}/notifications/${i}/reaction`,{},{headers:this.getHeaders()})}getStats(){return this.http.get(`${p.apiUrl}/notifications/stats`,{headers:this.getHeaders()})}updateUnreadCount(i){this.unreadCountSubject.next(i)}refreshUnreadCount(){return m(this,null,function*(){try{let i=yield this.getUnreadCount().toPromise();i&&this.updateUnreadCount(i.unread_count)}catch(i){console.error("Error refreshing unread count:",i)}})}getNotificationIcon(i){switch(i.type){case"evacuation_center_added":return"assets/evacuation-center-icon.png";case"emergency_alert":return"assets/emergency-icon.png";case"system_update":return"assets/system-icon.png";default:return"assets/alerto_icon.png"}}getNotificationColor(i){switch(i.type){case"evacuation_center_added":return"#42b883";case"emergency_alert":return"#e41e3f";case"system_update":return"#1877f2";default:return"#03b2dd"}}getTimeAgo(i){let s=new Date(i),a=Math.floor((new Date().getTime()-s.getTime())/1e3);return a<60?`${a}s`:a<3600?`${Math.floor(a/60)}m`:a<86400?`${Math.floor(a/3600)}h`:a<604800?`${Math.floor(a/86400)}d`:`${Math.floor(a/604800)}w`}static{this.\u0275fac=function(s){return new(s||c)(T(g))}}static{this.\u0275prov=I({token:c,factory:c.\u0275fac,providedIn:"root"})}}return c})();function Z(c,Q){if(c&1&&(e(0,"ion-badge",28),n(1),t()),c&2){let i=D();u(),H(" ",i.unreadNotificationCount>99?"99+":i.unreadNotificationCount," ")}}var de=(()=>{class c{constructor(i,s,r,a,B,Y){this.router=i,this.toastCtrl=s,this.http=r,this.notificationService=a,this.emergencyOverlay=B,this.modalCtrl=Y,this.unreadNotificationCount=0,this.notificationSubscription=null,this.pollSubscription=null,window.testEmergency=this.emergencyOverlay}ngOnInit(){this.notificationSubscription=this.notificationService.unreadCount$.subscribe(i=>{this.unreadNotificationCount=i}),this.loadUnreadCount(),this.pollSubscription=E(3e4).subscribe(()=>{this.notificationService.refreshUnreadCount()})}ngOnDestroy(){this.notificationSubscription&&this.notificationSubscription.unsubscribe(),this.pollSubscription&&this.pollSubscription.unsubscribe()}openDisasterMap(i){console.log(`\u{1F3E0} HOME: Opening disaster-specific map for: ${i}`);let s=i,r="";i==="earthquake"?(s="Earthquake",r="/earthquake-map"):i==="typhoon"?(s="Typhoon",r="/typhoon-map"):i==="flashflood"?(s="Flash Flood",r="/flood-map"):i==="fire"?(s="Fire",r="/fire-map"):i==="landslide"&&(s="Landslide",r="/landslide-map"),console.log(`\u{1F3E0} HOME: Navigating to ${r} for ${s}`),this.toastCtrl.create({message:`\u{1F5FA}\uFE0F Opening ${s} evacuation centers...`,duration:2e3,color:"primary"}).then(a=>a.present()),this.router.navigate([r])}viewMap(){console.log("\u{1F3E0} HOME: Opening complete evacuation centers map"),this.toastCtrl.create({message:"\u{1F5FA}\uFE0F Opening complete evacuation centers map...",duration:2e3,color:"secondary"}).then(i=>i.present()),this.router.navigate(["/all-maps"])}loadUnreadCount(){return m(this,null,function*(){try{yield this.notificationService.refreshUnreadCount(),this.notificationService.unreadCount$.subscribe(i=>{this.unreadNotificationCount=i})}catch(i){console.error("Error loading unread notification count:",i)}})}openNotifications(){this.router.navigate(["/notifications"])}openUserGuide(){return m(this,null,function*(){yield(yield this.modalCtrl.create({component:J,cssClass:"user-guide-modal"})).present()})}static{this.\u0275fac=function(s){return new(s||c)(d(U),d(L),d(g),d(G),d(R),d(y))}}static{this.\u0275cmp=f({type:c,selectors:[["app-home"]],standalone:!0,features:[x],decls:59,vars:2,consts:[[3,"translucent"],["slot","end"],[1,"notification-button",3,"click"],["name","notifications-outline"],["class","notification-badge",4,"ngIf"],[1,"home-content"],[1,"disaster-container"],[1,"welcome-section"],[1,"welcome-title"],[1,"welcome-subtitle"],[1,"disaster-grid"],[1,"disaster","earthquake",3,"click"],[1,"card-icon-wrapper"],["src","assets/earthquake-icon.svg","alt","Earthquake"],[1,"card-indicator"],[1,"disaster","typhoon",3,"click"],["src","assets/icon/bagyo.png","alt","Typhoon"],[1,"disaster","flood",3,"click"],["src","assets/icon/baha.jpg","alt","Flood"],[1,"disaster","fire",3,"click"],["src","assets/icon/fire.jpg","alt","Fire"],[1,"disaster","landslide",3,"click"],["src","assets/icon/lanslide.jpg","alt","Landslide"],[1,"map-button-container"],["expand","block",1,"see-map-btn",3,"click"],[1,"guide-button-container"],["expand","block",1,"user-guide-btn",3,"click"],["name","help-circle-outline","slot","start"],[1,"notification-badge"]],template:function(s,r){s&1&&(e(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-title"),n(3," ALERTO! "),t(),e(4,"ion-buttons",1)(5,"ion-button",2),l("click",function(){return r.openNotifications()}),o(6,"ion-icon",3),z(7,Z,2,1,"ion-badge",4),t()()()(),e(8,"ion-content")(9,"div",5)(10,"div",6)(11,"div",7)(12,"h2",8),n(13,"Choose Emergency Type"),t(),e(14,"p",9),n(15,"Select a disaster type to view evacuation centers"),t()(),e(16,"div",10)(17,"ion-card",11),l("click",function(){return r.openDisasterMap("earthquake")}),e(18,"ion-card-content")(19,"div",12),o(20,"img",13),t(),e(21,"ion-text"),n(22,"Earthquake"),t(),o(23,"div",14),t()(),e(24,"ion-card",15),l("click",function(){return r.openDisasterMap("typhoon")}),e(25,"ion-card-content")(26,"div",12),o(27,"img",16),t(),e(28,"ion-text"),n(29,"Typhoon"),t(),o(30,"div",14),t()(),e(31,"ion-card",17),l("click",function(){return r.openDisasterMap("flashflood")}),e(32,"ion-card-content")(33,"div",12),o(34,"img",18),t(),e(35,"ion-text"),n(36,"Flash Flood"),t(),o(37,"div",14),t()(),e(38,"ion-card",19),l("click",function(){return r.openDisasterMap("fire")}),e(39,"ion-card-content")(40,"div",12),o(41,"img",20),t(),e(42,"ion-text"),n(43,"Fire"),t(),o(44,"div",14),t()(),e(45,"ion-card",21),l("click",function(){return r.openDisasterMap("landslide")}),e(46,"ion-card-content")(47,"div",12),o(48,"img",22),t(),e(49,"ion-text"),n(50,"Landslide"),t(),o(51,"div",14),t()()(),e(52,"div",23)(53,"ion-button",24),l("click",function(){return r.viewMap()}),n(54," See the whole map "),t()(),e(55,"div",25)(56,"ion-button",26),l("click",function(){return r.openUserGuide()}),o(57,"ion-icon",27),n(58," User Guide "),t()()()()()),s&2&&(h("translucent",!0),u(7),h("ngIf",r.unreadNotificationCount>0))},dependencies:[S,$,_,v,N,A,C,M,O,q,P,w,b,F],styles:['@charset "UTF-8";.status-text[_ngcontent-%COMP%]{margin-left:8px}.home-content[_ngcontent-%COMP%]{padding:20px 16px;height:calc(100vh - 120px);overflow:hidden;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#f8fafc,#e2e8f0);position:relative}.home-content[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:0;background-image:radial-gradient(circle at 25% 25%,rgba(59,130,246,.05) 0%,transparent 50%),radial-gradient(circle at 75% 75%,rgba(34,197,94,.05) 0%,transparent 50%);pointer-events:none;z-index:0}ion-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3b82f6,#2563eb);box-shadow:0 4px 12px #3b82f633}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: transparent;--color: white}ion-title[_ngcontent-%COMP%]{text-align:center;font-family:Poppins,Arial,sans-serif;font-size:1.8rem;font-weight:800;letter-spacing:1.5px;color:#fff!important;text-shadow:0 2px 4px rgba(0,0,0,.2)}ion-title[_ngcontent-%COMP%]:after{content:"\\26a0\\fe0f";margin-left:8px;font-size:1.2rem}.disaster-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;gap:20px;margin:0;height:100%;position:relative;z-index:1}.welcome-section[_ngcontent-%COMP%]{text-align:center;margin-bottom:16px;padding:0 20px;position:relative;z-index:2}.welcome-title[_ngcontent-%COMP%]{font-size:1.4rem;font-weight:800;color:#1e293b;margin:0 0 8px;letter-spacing:.5px;text-shadow:0 2px 4px rgba(0,0,0,.1)}.welcome-subtitle[_ngcontent-%COMP%]{font-size:.9rem;color:#64748b;margin:0;font-weight:500;opacity:.8}.disaster[_ngcontent-%COMP%]{margin:0;cursor:pointer;transition:all .3s cubic-bezier(.4,0,.2,1);width:100%;height:130px;border-radius:16px;box-shadow:0 4px 12px #0000001a;overflow:hidden;position:relative}.disaster[_ngcontent-%COMP%]:hover{transform:translateY(-4px) scale(1.02);box-shadow:0 8px 25px #00000026}.disaster[_ngcontent-%COMP%]:active{transform:translateY(-2px) scale(1.01);transition:all .1s ease}.disaster[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;padding:16px 12px;height:100%;position:relative;z-index:2}.disaster[_ngcontent-%COMP%]   .card-icon-wrapper[_ngcontent-%COMP%]{position:relative;margin-bottom:8px}.disaster[_ngcontent-%COMP%]   .card-icon-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:12px;box-shadow:0 4px 12px #00000026;transition:all .3s ease;border:2px solid rgba(255,255,255,.3)}.disaster[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-size:.95rem;font-weight:700;letter-spacing:.3px;text-shadow:0 1px 2px rgba(0,0,0,.1);margin-top:4px;position:relative;z-index:2}.disaster[_ngcontent-%COMP%]   .card-indicator[_ngcontent-%COMP%]{position:absolute;bottom:8px;right:8px;width:8px;height:8px;background:#fffc;border-radius:50%;box-shadow:0 2px 4px #0003;transition:all .3s ease}.disaster[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:0;background:linear-gradient(135deg,#fff3,#ffffff0d);z-index:1;pointer-events:none}.disaster[_ngcontent-%COMP%]:hover   .card-icon-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{transform:scale(1.1) rotate(5deg);box-shadow:0 6px 16px #0003}.disaster[_ngcontent-%COMP%]:hover   .card-indicator[_ngcontent-%COMP%]{transform:scale(1.3);background:#fff}.earthquake[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff8c00,orange,#ffb84d);border:2px solid rgba(255,140,0,.3)}.earthquake[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{color:#fff!important}.earthquake[_ngcontent-%COMP%]:hover{box-shadow:0 8px 25px #ff8c004d}.typhoon[_ngcontent-%COMP%]{background:linear-gradient(135deg,#22c55e,#16a34a,#15803d);border:2px solid rgba(34,197,94,.3)}.typhoon[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{color:#fff!important}.typhoon[_ngcontent-%COMP%]:hover{box-shadow:0 8px 25px #22c55e4d}.flood[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3b82f6,#2563eb,#1d4ed8);border:2px solid rgba(59,130,246,.3)}.flood[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{color:#fff!important}.flood[_ngcontent-%COMP%]:hover{box-shadow:0 8px 25px #3b82f64d}.fire[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ef4444,#dc2626,#b91c1c);border:2px solid rgba(239,68,68,.3)}.fire[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{color:#fff!important}.fire[_ngcontent-%COMP%]:hover{box-shadow:0 8px 25px #ef44444d}.landslide[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f59e0b,#d97706,#b45309);border:2px solid rgba(245,158,11,.3)}.landslide[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{color:#fff!important}.landslide[_ngcontent-%COMP%]:hover{box-shadow:0 8px 25px #f59e0b4d}.view-map[_ngcontent-%COMP%]{margin-top:24px;--background: #00bfff}.view-map[_ngcontent-%COMP%]:hover{--background: #0090cc}.view-map[disabled][_ngcontent-%COMP%]{--background: #999}.disaster-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;grid-template-rows:repeat(3,1fr);gap:16px;padding:0 20px;max-width:420px;margin:0 auto;width:100%;position:relative}.disaster-grid[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:-12px 8px;background:#fff9;border-radius:20px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);box-shadow:0 8px 32px #0000001a;z-index:-1}.disaster.landslide[_ngcontent-%COMP%]{grid-column:1/3;max-width:200px;margin:0 auto}.map-button-container[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-top:24px;padding:0 20px;position:relative;z-index:1}.guide-button-container[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-top:16px;padding:0 20px;position:relative;z-index:1}.see-map-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#6366f1,#4f46e5,#4338ca);color:#fff;border-radius:30px;font-weight:700;font-size:1.1rem;height:56px;max-width:320px;text-transform:none;transition:all .3s cubic-bezier(.4,0,.2,1);cursor:pointer;box-shadow:0 6px 20px #6366f14d;border:2px solid rgba(255,255,255,.2);letter-spacing:.5px;position:relative;overflow:hidden}.see-map-btn[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s ease}.see-map-btn[_ngcontent-%COMP%]:hover{transform:translateY(-3px) scale(1.02);box-shadow:0 10px 30px #6366f166;background:linear-gradient(135deg,#7c3aed,#6366f1,#4f46e5)}.see-map-btn[_ngcontent-%COMP%]:hover:before{left:100%}.see-map-btn[_ngcontent-%COMP%]:active{transform:translateY(-1px) scale(1.01);box-shadow:0 6px 20px #6366f14d;transition:all .1s ease}.see-map-btn[disabled][_ngcontent-%COMP%]{background:linear-gradient(135deg,#9ca3af,#6b7280);color:#d1d5db;cursor:not-allowed;box-shadow:0 2px 8px #0000001a}.see-map-btn[disabled][_ngcontent-%COMP%]:hover{transform:none;box-shadow:0 2px 8px #0000001a}.see-map-btn[disabled][_ngcontent-%COMP%]:before{display:none}.see-map-btn[_ngcontent-%COMP%]:after{content:"\\1f5fa\\fe0f";margin-left:8px;font-size:1.2rem}.user-guide-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#10b981,#059669,#047857);color:#fff;border-radius:30px;font-weight:600;font-size:1rem;height:50px;max-width:280px;text-transform:none;transition:all .3s cubic-bezier(.4,0,.2,1);cursor:pointer;box-shadow:0 4px 16px #10b9814d;border:2px solid rgba(255,255,255,.2);letter-spacing:.3px;position:relative;overflow:hidden}.user-guide-btn[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s ease}.user-guide-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px) scale(1.01);box-shadow:0 8px 24px #10b98166;background:linear-gradient(135deg,#059669,#047857,#065f46)}.user-guide-btn[_ngcontent-%COMP%]:hover:before{left:100%}.user-guide-btn[_ngcontent-%COMP%]:active{transform:translateY(-1px) scale(1.005);box-shadow:0 4px 16px #10b9814d;transition:all .1s ease}.user-guide-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:1.2rem;margin-right:8px}.notifications-section[_ngcontent-%COMP%]{margin-top:20px;border-top:1px solid var(--ion-color-light);padding-top:10px}ion-item-divider[_ngcontent-%COMP%]{--background: transparent;--color: var(--ion-color-primary);font-weight:700;font-size:1.1rem;letter-spacing:.5px;margin-bottom:8px}.notification-button[_ngcontent-%COMP%]{position:relative;--color: white;--background: rgba(255, 255, 255, .1);--border-radius: 12px;transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2)}.notification-button[_ngcontent-%COMP%]:hover{--background: rgba(255, 255, 255, .2);transform:scale(1.05)}.notification-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:1.4rem;color:#fff;filter:drop-shadow(0 2px 4px rgba(0,0,0,.2))}.notification-badge[_ngcontent-%COMP%]{position:absolute;top:6px;right:6px;background:linear-gradient(135deg,#ef4444,#dc2626);color:#fff;font-size:10px;font-weight:700;min-width:18px;height:18px;border-radius:9px;display:flex;align-items:center;justify-content:center;z-index:10;box-shadow:0 2px 8px #ef444466;border:2px solid white;animation:_ngcontent-%COMP%_pulse-notification 2s infinite}@keyframes _ngcontent-%COMP%_pulse-notification{0%{transform:scale(1);box-shadow:0 2px 8px #ef444466}50%{transform:scale(1.1);box-shadow:0 4px 12px #ef444499}to{transform:scale(1);box-shadow:0 2px 8px #ef444466}}']})}}return c})(),J=(()=>{class c{constructor(i){this.modalCtrl=i}dismiss(){this.modalCtrl.dismiss()}static{this.\u0275fac=function(s){return new(s||c)(d(y))}}static{this.\u0275cmp=f({type:c,selectors:[["ng-component"]],standalone:!0,features:[x],decls:138,vars:0,consts:[[1,"modal-title"],["slot","end"],[3,"click"],[1,"ion-padding"],[1,"guide-section"],[1,"section-title"],[1,"section-content"],[1,"feature-list"],[1,"feature-item"],["src","assets/home1.png",1,"feature-icon"],[1,"feature-text"],["src","assets/map1.png",1,"feature-icon"],["src","assets/search1.png",1,"feature-icon"],["src","assets/lamp1.png",1,"feature-icon"],[1,"steps-list"],[1,"step-item"],[1,"step-number"],[1,"step-content"],[1,"disaster-grid"],[1,"disaster-type"],["src","assets/earthquake-icon.svg",1,"disaster-icon"],["src","assets/icon/bagyo.png",1,"disaster-icon"],["src","assets/flood.png",1,"disaster-icon"],["src","assets/fireIcon.png",1,"disaster-icon"],["src","assets/landslideIcon.png",1,"disaster-icon"],["src","assets/otherdisasterIcon.png",1,"disaster-icon"],[1,"tips-list"],[1,"tip-item"],["name","call-outline",1,"tip-icon"],["name","location-outline",1,"tip-icon"],["name","download-outline",1,"tip-icon"],["name","notifications-outline",1,"tip-icon"]],template:function(s,r){s&1&&(e(0,"ion-header")(1,"ion-toolbar")(2,"ion-title",0),n(3,"ALERTO User Guide"),t(),e(4,"ion-buttons",1)(5,"ion-button",2),l("click",function(){return r.dismiss()}),n(6,"Close"),t()()()(),e(7,"ion-content",3)(8,"div",4)(9,"h3",5),n(10,"\u{1F6E1}\uFE0F Welcome to ALERTO - The Safe Zone"),t(),e(11,"p",6),n(12," ALERTO is your comprehensive disaster preparedness and evacuation assistance app. Our mission is to keep you safe by providing real-time access to evacuation centers, emergency contacts, and safety information during natural disasters. "),t()(),e(13,"div",4)(14,"h3",5),n(15,"\u2728 Main Features"),t(),e(16,"div",7)(17,"div",8),o(18,"img",9),e(19,"div",10)(20,"h4"),n(21,"Disaster Selection"),t(),e(22,"p"),n(23,"Choose from 6 disaster types to find specific evacuation centers"),t()()(),e(24,"div",8),o(25,"img",11),e(26,"div",10)(27,"h4"),n(28,"Interactive Maps"),t(),e(29,"p"),n(30,"View evacuation centers with real-time navigation and routing"),t()()(),e(31,"div",8),o(32,"img",12),e(33,"div",10)(34,"h4"),n(35,"Location Search"),t(),e(36,"p"),n(37,"Find specific locations and nearby evacuation centers"),t()()(),e(38,"div",8),o(39,"img",13),e(40,"div",10)(41,"h4"),n(42,"Safety Tips & Contacts"),t(),e(43,"p"),n(44,"Access emergency contacts and disaster-specific safety information"),t()()()()(),e(45,"div",4)(46,"h3",5),n(47,"\u{1F4F1} How to Use ALERTO"),t(),e(48,"div",14)(49,"div",15)(50,"div",16),n(51,"1"),t(),e(52,"div",17)(53,"h4"),n(54,"Select Disaster Type"),t(),e(55,"p"),n(56,"From the home screen, tap on the disaster type you need help with (Earthquake, Typhoon, Flood, Fire, Landslide, or General)"),t()()(),e(57,"div",15)(58,"div",16),n(59,"2"),t(),e(60,"div",17)(61,"h4"),n(62,"View Evacuation Centers"),t(),e(63,"p"),n(64,"The map will show evacuation centers specific to your selected disaster type with your current location"),t()()(),e(65,"div",15)(66,"div",16),n(67,"3"),t(),e(68,"div",17)(69,"h4"),n(70,"Use Map Controls"),t(),e(71,"p"),n(72,"\u2022 Tap the house icon to see all centers list"),o(73,"br"),n(74,"\u2022 Tap download to save map offline"),o(75,"br"),n(76,"\u2022 Tap compass to route to 2 nearest centers"),t()()(),e(77,"div",15)(78,"div",16),n(79,"4"),t(),e(80,"div",17)(81,"h4"),n(82,"Navigate to Safety"),t(),e(83,"p"),n(84,"Choose walking, cycling, or driving routes to reach your selected evacuation center safely"),t()()()()(),e(85,"div",4)(86,"h3",5),n(87,"\u{1F32A}\uFE0F Supported Disaster Types"),t(),e(88,"div",18)(89,"div",19),o(90,"img",20),e(91,"span"),n(92,"Earthquake"),t()(),e(93,"div",19),o(94,"img",21),e(95,"span"),n(96,"Typhoon"),t()(),e(97,"div",19),o(98,"img",22),e(99,"span"),n(100,"Flood"),t()(),e(101,"div",19),o(102,"img",23),e(103,"span"),n(104,"Fire"),t()(),e(105,"div",19),o(106,"img",24),e(107,"span"),n(108,"Landslide"),t()(),e(109,"div",19),o(110,"img",25),e(111,"span"),n(112,"General"),t()()()(),e(113,"div",4)(114,"h3",5),n(115,"\u{1F6A8} Emergency Tips"),t(),e(116,"div",26)(117,"div",27),o(118,"ion-icon",28),e(119,"p"),n(120,"Always call 911 for immediate emergency assistance"),t()(),e(121,"div",27),o(122,"ion-icon",29),e(123,"p"),n(124,"Enable location services for accurate evacuation center directions"),t()(),e(125,"div",27),o(126,"ion-icon",30),e(127,"p"),n(128,"Download maps when you have internet for offline access during emergencies"),t()(),e(129,"div",27),o(130,"ion-icon",31),e(131,"p"),n(132,"Keep notifications enabled to receive emergency alerts"),t()()()(),e(133,"div",4)(134,"h3",5),n(135,"\u{1F4DE} Need Help?"),t(),e(136,"p",6),n(137," For technical support or questions about ALERTO, visit the Tips tab for emergency contacts and safety information. Stay safe and prepared! "),t()()())},dependencies:[S,_,v,C,M,O,P,w,b],styles:[".modal-title[_ngcontent-%COMP%]{font-size:1.3rem;font-weight:700;color:var(--ion-color-primary)}.guide-section[_ngcontent-%COMP%]{margin-bottom:25px;padding-bottom:20px;border-bottom:1px solid var(--ion-color-light)}.guide-section[_ngcontent-%COMP%]:last-child{border-bottom:none}.section-title[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:600;margin-bottom:15px;color:var(--ion-color-primary)}.section-content[_ngcontent-%COMP%]{font-size:.95rem;line-height:1.5;color:var(--ion-color-medium-shade)}.feature-list[_ngcontent-%COMP%], .steps-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:15px}.feature-item[_ngcontent-%COMP%], .step-item[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:15px;padding:12px;background:var(--ion-color-light);border-radius:10px}.feature-icon[_ngcontent-%COMP%]{width:32px;height:32px;object-fit:contain;flex-shrink:0}.feature-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .step-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:1rem;font-weight:600;margin-bottom:5px;color:var(--ion-color-dark)}.feature-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .step-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.9rem;color:var(--ion-color-medium-shade);margin:0;line-height:1.4}.step-number[_ngcontent-%COMP%]{width:30px;height:30px;background:var(--ion-color-primary);color:#fff;border-radius:50%;display:flex;align-items:center;justify-content:center;font-weight:700;font-size:.9rem;flex-shrink:0}.disaster-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(3,1fr);gap:15px}.disaster-type[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:8px;padding:15px;background:var(--ion-color-light);border-radius:10px;text-align:center}.disaster-icon[_ngcontent-%COMP%]{width:40px;height:40px;object-fit:contain}.disaster-type[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:.85rem;font-weight:500;color:var(--ion-color-dark)}.tips-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.tip-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:10px;background:var(--ion-color-light);border-radius:8px}.tip-icon[_ngcontent-%COMP%]{color:var(--ion-color-primary);font-size:1.2rem;flex-shrink:0}.tip-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:.9rem;color:var(--ion-color-medium-shade)}"]})}}return c})();export{de as HomePage,J as UserGuideModalComponent};
